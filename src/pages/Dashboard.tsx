import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/ui/page-header';
import { MetricCard, MetricGrid } from '@/components/ui/metric-card';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  LineChart,
  Line,
  Area,
  AreaChart
} from 'recharts';
import { 
  ClipboardList, 
  CheckCircle, 
  Users, 
  DollarSign, 
  Clock, 
  TrendingUp,
  Calendar,
  Filter
} from 'lucide-react';
import { mockDashboardMetrics } from '@/data/mockData';
import { format } from 'date-fns';

type DateRange = 'daily' | 'weekly' | 'monthly' | 'quarterly';

export function Dashboard() {
  const [dateRange, setDateRange] = useState<DateRange>('monthly');
  const [selectedCarrier, setSelectedCarrier] = useState<string>('all');

  const metrics = mockDashboardMetrics;

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  };

  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('en-US').format(value);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Dashboard"
        description="Overview of claims, performance metrics, and key insights"
      >
        <div className="flex items-center gap-4">
          <Select value={dateRange} onValueChange={(value: DateRange) => setDateRange(value)}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="quarterly">Quarterly</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>
      </PageHeader>

      <PageContent>
        {/* Key Metrics */}
        <MetricGrid className="mb-8">
          <MetricCard
            title="Open Claims"
            value={formatNumber(metrics.openClaims)}
            change={{ value: 12, type: 'increase', period: 'last month' }}
            icon={ClipboardList}
            trend="up"
          />
          <MetricCard
            title="Closed Claims"
            value={formatNumber(metrics.closedClaims)}
            change={{ value: 8, type: 'increase', period: 'last month' }}
            icon={CheckCircle}
            trend="up"
          />
          <MetricCard
            title="Active Consultations"
            value={formatNumber(metrics.activeConsultations)}
            change={{ value: 3, type: 'decrease', period: 'last week' }}
            icon={Users}
            trend="down"
          />
          <MetricCard
            title="Total Revenue"
            value={formatCurrency(metrics.totalRevenue)}
            change={{ value: 15, type: 'increase', period: 'last quarter' }}
            icon={DollarSign}
            trend="up"
          />
        </MetricGrid>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {/* Claims by Status */}
          <Card className="col-span-1">
            <CardHeader>
              <CardTitle>Claims by Status</CardTitle>
              <CardDescription>Distribution of current claim statuses</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={metrics.claimsByStatus}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percentage }) => `${name} ${percentage}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="count"
                  >
                    {metrics.claimsByStatus.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Monthly Claims Trend */}
          <Card className="col-span-2">
            <CardHeader>
              <CardTitle>Claims Trend</CardTitle>
              <CardDescription>Monthly open vs closed claims</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <AreaChart data={metrics.claimsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value, name) => [formatNumber(Number(value)), name]} />
                  <Area 
                    type="monotone" 
                    dataKey="openClaims" 
                    stackId="1" 
                    stroke="#8884d8" 
                    fill="#8884d8" 
                    name="Open Claims"
                  />
                  <Area 
                    type="monotone" 
                    dataKey="closedClaims" 
                    stackId="1" 
                    stroke="#82ca9d" 
                    fill="#82ca9d" 
                    name="Closed Claims"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Revenue Trend */}
          <Card className="col-span-2">
            <CardHeader>
              <CardTitle>Revenue Trend</CardTitle>
              <CardDescription>Monthly revenue performance</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={metrics.claimsByMonth}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis tickFormatter={(value) => formatCurrency(value)} />
                  <Tooltip formatter={(value) => [formatCurrency(Number(value)), 'Revenue']} />
                  <Line 
                    type="monotone" 
                    dataKey="revenue" 
                    stroke="#ff7300" 
                    strokeWidth={3}
                    dot={{ fill: '#ff7300' }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Top Adjusters */}
          <Card className="col-span-1">
            <CardHeader>
              <CardTitle>Top Adjusters</CardTitle>
              <CardDescription>Performance by adjuster</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {metrics.topAdjusters.map((adjuster, index) => (
                  <div key={adjuster.adjusterId} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                        {index + 1}
                      </div>
                      <div>
                        <p className="font-medium">{adjuster.adjusterName}</p>
                        <p className="text-sm text-muted-foreground">
                          {adjuster.totalClaims} claims
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(adjuster.revenue)}</p>
                      <p className="text-sm text-muted-foreground">
                        ⭐ {adjuster.rating}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card className="mt-6">
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
            <CardDescription>Latest updates and actions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {metrics.recentActivity.map((activity) => (
                <div key={activity.id} className="flex items-start gap-4 p-4 rounded-lg border">
                  <div className="w-2 h-2 rounded-full bg-primary mt-2"></div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">{activity.title}</h4>
                      <span className="text-sm text-muted-foreground">
                        {format(activity.timestamp, 'MMM dd, HH:mm')}
                      </span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {activity.description}
                    </p>
                    {activity.userName && (
                      <p className="text-xs text-muted-foreground mt-1">
                        by {activity.userName}
                      </p>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </PageContent>
    </div>
  );
}
