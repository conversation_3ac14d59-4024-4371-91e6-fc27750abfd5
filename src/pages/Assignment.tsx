import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>er, PageContent } from '@/components/ui/page-header';
import { DataTable, Column } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/ui/modal';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Plus,
  Edit,
  Trash2,
  Eye,
  DollarSign,
  Calendar,
  MapPin,
  User,
  Building2,
  Clock,
  AlertTriangle,
  CheckCircle,
  FileText,
  Phone,
  Mail
} from 'lucide-react';
import { mockClaims, mockCarriers, mockAdjusters, mockContractors } from '@/data/mockData';
import { Claim } from '@/types';
import { format } from 'date-fns';

export function AssignmentPage() {
  const [claims, setClaims] = useState<Claim[]>(mockClaims);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [editingClaim, setEditingClaim] = useState<Claim | null>(null);
  const [viewingClaim, setViewingClaim] = useState<Claim | null>(null);
  const [formData, setFormData] = useState<Partial<Claim>>({});

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Open':
        return 'bg-blue-100 text-blue-800';
      case 'In Progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'Under Review':
        return 'bg-purple-100 text-purple-800';
      case 'Approved':
        return 'bg-green-100 text-green-800';
      case 'Denied':
        return 'bg-red-100 text-red-800';
      case 'Closed':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-100 text-red-800';
      case 'High':
        return 'bg-orange-100 text-orange-800';
      case 'Medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'Low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getCarrierName = (carrierId: string) => {
    const carrier = mockCarriers.find(c => c.id === carrierId);
    return carrier ? carrier.name : 'Unknown';
  };

  const getAdjusterName = (adjusterId?: string) => {
    if (!adjusterId) return 'Unassigned';
    const adjuster = mockAdjusters.find(a => a.id === adjusterId);
    return adjuster ? `${adjuster.firstName} ${adjuster.lastName}` : 'Unknown';
  };

  const columns: Column<Claim>[] = [
    {
      key: 'claimNumber',
      header: 'Claim Number',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <FileText className="h-5 w-5 text-primary" />
          </div>
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-muted-foreground">{row.policyNumber}</div>
          </div>
        </div>
      )
    },
    {
      key: 'policyHolderName',
      header: 'Policy Holder',
      sortable: true,
      render: (value, row) => (
        <div>
          <div className="font-medium">{value}</div>
          <div className="text-sm text-muted-foreground">{row.claimType}</div>
        </div>
      )
    },
    {
      key: 'carrierId',
      header: 'Carrier',
      render: (value) => (
        <div className="flex items-center gap-2">
          <Building2 className="h-4 w-4" />
          <span className="text-sm">{getCarrierName(value)}</span>
        </div>
      )
    },
    {
      key: 'adjusterId',
      header: 'Adjuster',
      render: (value) => (
        <div className="flex items-center gap-2">
          <User className="h-4 w-4" />
          <span className="text-sm">{getAdjusterName(value)}</span>
        </div>
      )
    },
    {
      key: 'status',
      header: 'Status',
      sortable: true,
      render: (value) => (
        <Badge className={getStatusColor(value)}>
          {value}
        </Badge>
      )
    },
    {
      key: 'priority',
      header: 'Priority',
      sortable: true,
      render: (value) => (
        <Badge className={getPriorityColor(value)}>
          {value}
        </Badge>
      )
    },
    {
      key: 'estimatedAmount',
      header: 'Estimated Amount',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-2">
          <DollarSign className="h-4 w-4" />
          <span className="font-medium">${value.toLocaleString()}</span>
        </div>
      )
    },
    {
      key: 'incidentDate',
      header: 'Incident Date',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-3 w-3" />
          {format(new Date(value), 'MMM dd, yyyy')}
        </div>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleView(row)}
          >
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    setEditingClaim(null);
    setFormData({});
    setIsModalOpen(true);
  };

  const handleEdit = (claim: Claim) => {
    setEditingClaim(claim);
    setFormData(claim);
    setIsModalOpen(true);
  };

  const handleView = (claim: Claim) => {
    setViewingClaim(claim);
    setIsViewModalOpen(true);
  };

  const handleDelete = (id: string) => {
    setClaims(claims.filter(c => c.id !== id));
  };

  const handleSave = () => {
    if (editingClaim) {
      // Update existing claim
      setClaims(claims.map(c =>
        c.id === editingClaim.id ? { ...c, ...formData } : c
      ));
    } else {
      // Add new claim
      const newClaim: Claim = {
        id: Date.now().toString(),
        claimNumber: formData.claimNumber || `CLM-${Date.now()}`,
        carrierId: formData.carrierId || '',
        adjusterId: formData.adjusterId,
        contractorId: formData.contractorId,
        policyNumber: formData.policyNumber || '',
        policyHolderName: formData.policyHolderName || '',
        policyHolderPhone: formData.policyHolderPhone || '',
        policyHolderEmail: formData.policyHolderEmail || '',
        incidentDate: formData.incidentDate || new Date(),
        reportedDate: formData.reportedDate || new Date(),
        claimType: formData.claimType || '',
        category: formData.category || '',
        subcategory: formData.subcategory,
        status: formData.status as any || 'Open',
        priority: formData.priority as any || 'Medium',
        description: formData.description || '',
        damageDescription: formData.damageDescription || '',
        estimatedAmount: formData.estimatedAmount || 0,
        approvedAmount: formData.approvedAmount,
        deductible: formData.deductible || 0,
        location: formData.location || {
          street: '',
          city: '',
          state: '',
          zipCode: ''
        },
        assignedDate: formData.assignedDate,
        completedDate: formData.completedDate,
        notes: formData.notes || [],
        documents: formData.documents || [],
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setClaims([...claims, newClaim]);
    }
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Claims Management"
        description="Manage insurance claims, assignments, and processing"
      >
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          New Claim
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={claims}
          columns={columns}
          searchPlaceholder="Search claims..."
          onRowClick={(claim) => handleView(claim)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingClaim ? 'Edit Claim' : 'Create New Claim'}
        size="xl"
        footer={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {editingClaim ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="claimNumber">Claim Number</Label>
            <Input
              id="claimNumber"
              value={formData.claimNumber || ''}
              onChange={(e) => setFormData({ ...formData, claimNumber: e.target.value })}
              placeholder="Enter claim number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="policyNumber">Policy Number</Label>
            <Input
              id="policyNumber"
              value={formData.policyNumber || ''}
              onChange={(e) => setFormData({ ...formData, policyNumber: e.target.value })}
              placeholder="Enter policy number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="policyHolderName">Policy Holder Name</Label>
            <Input
              id="policyHolderName"
              value={formData.policyHolderName || ''}
              onChange={(e) => setFormData({ ...formData, policyHolderName: e.target.value })}
              placeholder="Enter policy holder name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="policyHolderEmail">Email</Label>
            <Input
              id="policyHolderEmail"
              type="email"
              value={formData.policyHolderEmail || ''}
              onChange={(e) => setFormData({ ...formData, policyHolderEmail: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="carrierId">Carrier</Label>
            <Select
              value={formData.carrierId || ''}
              onValueChange={(value) => setFormData({ ...formData, carrierId: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select carrier" />
              </SelectTrigger>
              <SelectContent>
                {mockCarriers.map((carrier) => (
                  <SelectItem key={carrier.id} value={carrier.id}>
                    {carrier.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="adjusterId">Adjuster</Label>
            <Select
              value={formData.adjusterId || ''}
              onValueChange={(value) => setFormData({ ...formData, adjusterId: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select adjuster" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Unassigned</SelectItem>
                {mockAdjusters.map((adjuster) => (
                  <SelectItem key={adjuster.id} value={adjuster.id}>
                    {adjuster.firstName} {adjuster.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status || ''}
              onValueChange={(value) => setFormData({ ...formData, status: value as any })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Open">Open</SelectItem>
                <SelectItem value="In Progress">In Progress</SelectItem>
                <SelectItem value="Under Review">Under Review</SelectItem>
                <SelectItem value="Approved">Approved</SelectItem>
                <SelectItem value="Denied">Denied</SelectItem>
                <SelectItem value="Closed">Closed</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select
              value={formData.priority || ''}
              onValueChange={(value) => setFormData({ ...formData, priority: value as any })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
                <SelectItem value="Critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="space-y-2">
            <Label htmlFor="estimatedAmount">Estimated Amount</Label>
            <Input
              id="estimatedAmount"
              type="number"
              value={formData.estimatedAmount || ''}
              onChange={(e) => setFormData({ ...formData, estimatedAmount: parseFloat(e.target.value) || 0 })}
              placeholder="Enter estimated amount"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="deductible">Deductible</Label>
            <Input
              id="deductible"
              type="number"
              value={formData.deductible || ''}
              onChange={(e) => setFormData({ ...formData, deductible: parseFloat(e.target.value) || 0 })}
              placeholder="Enter deductible amount"
            />
          </div>
          <div className="space-y-2 col-span-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description || ''}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Enter claim description"
              rows={3}
            />
          </div>
        </div>
      </Modal>

      {/* View Modal */}
      {viewingClaim && (
        <Modal
          isOpen={isViewModalOpen}
          onClose={() => setIsViewModalOpen(false)}
          title={`Claim Details - ${viewingClaim.claimNumber}`}
          size="xl"
        >
          <Tabs defaultValue="overview" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
              <TabsTrigger value="notes">Notes</TabsTrigger>
              <TabsTrigger value="documents">Documents</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Claim Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Claim Number:</span>
                      <span className="font-medium">{viewingClaim.claimNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Policy Number:</span>
                      <span className="font-medium">{viewingClaim.policyNumber}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Status:</span>
                      <Badge className={getStatusColor(viewingClaim.status)}>
                        {viewingClaim.status}
                      </Badge>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Priority:</span>
                      <Badge className={getPriorityColor(viewingClaim.priority)}>
                        {viewingClaim.priority}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Financial Information</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Estimated Amount:</span>
                      <span className="font-medium">${viewingClaim.estimatedAmount.toLocaleString()}</span>
                    </div>
                    {viewingClaim.approvedAmount && (
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Approved Amount:</span>
                        <span className="font-medium">${viewingClaim.approvedAmount.toLocaleString()}</span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Deductible:</span>
                      <span className="font-medium">${viewingClaim.deductible.toLocaleString()}</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="details" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Policy Holder Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="font-medium">{viewingClaim.policyHolderName}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Phone:</span>
                    <span className="font-medium">{viewingClaim.policyHolderPhone}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email:</span>
                    <span className="font-medium">{viewingClaim.policyHolderEmail}</span>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="notes" className="space-y-4">
              <div className="text-center text-muted-foreground py-8">
                No notes available for this claim.
              </div>
            </TabsContent>

            <TabsContent value="documents" className="space-y-4">
              <div className="text-center text-muted-foreground py-8">
                No documents uploaded for this claim.
              </div>
            </TabsContent>
          </Tabs>
        </Modal>
      )}
    </div>
  );
}