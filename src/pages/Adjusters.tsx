import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/ui/page-header';
import { DataTable, Column } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/ui/modal';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Star, Phone, Mail, MapPin, Calendar } from 'lucide-react';
import { mockAdjusters } from '@/data/mockData';
import { Adjuster } from '@/types';
import { format } from 'date-fns';

export function Adjusters() {
  const [adjusters, setAdjusters] = useState<Adjuster[]>(mockAdjusters);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingAdjuster, setEditingAdjuster] = useState<Adjuster | null>(null);
  const [formData, setFormData] = useState<Partial<Adjuster>>({});

  const columns: Column<Adjuster>[] = [
    {
      key: 'firstName',
      header: 'Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
            <span className="font-semibold text-primary">
              {row.firstName[0]}{row.lastName[0]}
            </span>
          </div>
          <div>
            <div className="font-medium">{row.firstName} {row.lastName}</div>
            <div className="text-sm text-muted-foreground">{row.licenseNumber}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      header: 'Contact',
      render: (value, row) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3" />
            {value}
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-3 w-3" />
            {row.phone}
          </div>
        </div>
      )
    },
    {
      key: 'specializations',
      header: 'Specializations',
      render: (value: string[]) => (
        <div className="flex flex-wrap gap-1">
          {value.slice(0, 2).map((spec) => (
            <Badge key={spec} variant="secondary" className="text-xs">
              {spec}
            </Badge>
          ))}
          {value.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{value.length - 2}
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'rating',
      header: 'Rating',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'totalAssignments',
      header: 'Assignments',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value.toLocaleString()}</span>
      )
    },
    {
      key: 'avgCompletionTime',
      header: 'Avg. Time',
      sortable: true,
      render: (value) => (
        <span className="text-sm">{value} days</span>
      )
    },
    {
      key: 'licenseExpiry',
      header: 'License Expiry',
      render: (value) => (
        <div className="flex items-center gap-2 text-sm">
          <Calendar className="h-3 w-3" />
          {format(new Date(value), 'MMM dd, yyyy')}
        </div>
      )
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    setEditingAdjuster(null);
    setFormData({});
    setIsModalOpen(true);
  };

  const handleEdit = (adjuster: Adjuster) => {
    setEditingAdjuster(adjuster);
    setFormData(adjuster);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    setAdjusters(adjusters.filter(a => a.id !== id));
  };

  const handleSave = () => {
    if (editingAdjuster) {
      // Update existing adjuster
      setAdjusters(adjusters.map(a => 
        a.id === editingAdjuster.id ? { ...a, ...formData } : a
      ));
    } else {
      // Add new adjuster
      const newAdjuster: Adjuster = {
        id: Date.now().toString(),
        firstName: formData.firstName || '',
        lastName: formData.lastName || '',
        email: formData.email || '',
        phone: formData.phone || '',
        specializations: formData.specializations || [],
        assignedCarriers: formData.assignedCarriers || [],
        licenseNumber: formData.licenseNumber || '',
        licenseExpiry: formData.licenseExpiry || new Date(),
        isActive: formData.isActive ?? true,
        rating: formData.rating || 0,
        totalAssignments: formData.totalAssignments || 0,
        avgCompletionTime: formData.avgCompletionTime || 0,
        address: formData.address || {
          street: '',
          city: '',
          state: '',
          zipCode: ''
        },
        emergencyContact: formData.emergencyContact || {
          name: '',
          phone: '',
          relationship: ''
        },
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setAdjusters([...adjusters, newAdjuster]);
    }
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Adjusters"
        description="Manage insurance adjusters and their assignments"
      >
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Adjuster
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={adjusters}
          columns={columns}
          searchPlaceholder="Search adjusters..."
          onRowClick={(adjuster) => console.log('View adjuster:', adjuster)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingAdjuster ? 'Edit Adjuster' : 'Add New Adjuster'}
        size="lg"
        footer={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {editingAdjuster ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="firstName">First Name</Label>
            <Input
              id="firstName"
              value={formData.firstName || ''}
              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              placeholder="Enter first name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="lastName">Last Name</Label>
            <Input
              id="lastName"
              value={formData.lastName || ''}
              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              placeholder="Enter last name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email || ''}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone || ''}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="Enter phone number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="licenseNumber">License Number</Label>
            <Input
              id="licenseNumber"
              value={formData.licenseNumber || ''}
              onChange={(e) => setFormData({ ...formData, licenseNumber: e.target.value })}
              placeholder="Enter license number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="rating">Rating</Label>
            <Input
              id="rating"
              type="number"
              min="0"
              max="5"
              step="0.1"
              value={formData.rating || ''}
              onChange={(e) => setFormData({ ...formData, rating: parseFloat(e.target.value) })}
              placeholder="Enter rating (0-5)"
            />
          </div>
          <div className="flex items-center space-x-2 col-span-2">
            <Switch
              id="isActive"
              checked={formData.isActive ?? true}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>
      </Modal>
    </div>
  );
}
