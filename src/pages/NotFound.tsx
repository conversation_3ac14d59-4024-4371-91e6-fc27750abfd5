import { useLocation } from "react-router-dom";
import { useEffect } from "react";

export function NotFound(){
  const location = useLocation();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  return (
    <div className="flex h-screen items-center justify-center">
      <h1 className="text-4xl font-bold">404 - Not Found</h1>
    </div>
  );
};