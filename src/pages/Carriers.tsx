import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, PageContent } from '@/components/ui/page-header';
import { DataTable, Column } from '@/components/ui/data-table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Modal } from '@/components/ui/modal';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Plus, Edit, Trash2, Star, Phone, Mail, MapPin } from 'lucide-react';
import { mockCarriers } from '@/data/mockData';
import { Carrier } from '@/types';
import { format } from 'date-fns';

export function Carriers() {
  const [carriers, setCarriers] = useState<Carrier[]>(mockCarriers);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingCarrier, setEditingCarrier] = useState<Carrier | null>(null);
  const [formData, setFormData] = useState<Partial<Carrier>>({});

  const columns: Column<Carrier>[] = [
    {
      key: 'name',
      header: 'Company Name',
      sortable: true,
      render: (value, row) => (
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center">
            <span className="font-semibold text-primary">{row.code}</span>
          </div>
          <div>
            <div className="font-medium">{value}</div>
            <div className="text-sm text-muted-foreground">{row.contactPerson}</div>
          </div>
        </div>
      )
    },
    {
      key: 'email',
      header: 'Contact',
      render: (value, row) => (
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-sm">
            <Mail className="h-3 w-3" />
            {value}
          </div>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Phone className="h-3 w-3" />
            {row.phone}
          </div>
        </div>
      )
    },
    {
      key: 'address',
      header: 'Location',
      render: (value) => (
        <div className="flex items-center gap-2 text-sm">
          <MapPin className="h-3 w-3" />
          {value.city}, {value.state}
        </div>
      )
    },
    {
      key: 'policyTypes',
      header: 'Policy Types',
      render: (value: string[]) => (
        <div className="flex flex-wrap gap-1">
          {value.slice(0, 2).map((type) => (
            <Badge key={type} variant="secondary" className="text-xs">
              {type}
            </Badge>
          ))}
          {value.length > 2 && (
            <Badge variant="outline" className="text-xs">
              +{value.length - 2}
            </Badge>
          )}
        </div>
      )
    },
    {
      key: 'rating',
      header: 'Rating',
      sortable: true,
      render: (value) => (
        <div className="flex items-center gap-1">
          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
          <span className="font-medium">{value}</span>
        </div>
      )
    },
    {
      key: 'totalClaims',
      header: 'Total Claims',
      sortable: true,
      render: (value) => (
        <span className="font-medium">{value.toLocaleString()}</span>
      )
    },
    {
      key: 'isActive',
      header: 'Status',
      render: (value) => (
        <Badge variant={value ? 'default' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      )
    },
    {
      key: 'actions',
      header: 'Actions',
      render: (_, row) => (
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleEdit(row)}
          >
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDelete(row.id)}
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      )
    }
  ];

  const handleAdd = () => {
    setEditingCarrier(null);
    setFormData({});
    setIsModalOpen(true);
  };

  const handleEdit = (carrier: Carrier) => {
    setEditingCarrier(carrier);
    setFormData(carrier);
    setIsModalOpen(true);
  };

  const handleDelete = (id: string) => {
    setCarriers(carriers.filter(c => c.id !== id));
  };

  const handleSave = () => {
    if (editingCarrier) {
      // Update existing carrier
      setCarriers(carriers.map(c => 
        c.id === editingCarrier.id ? { ...c, ...formData } : c
      ));
    } else {
      // Add new carrier
      const newCarrier: Carrier = {
        id: Date.now().toString(),
        name: formData.name || '',
        code: formData.code || '',
        contactPerson: formData.contactPerson || '',
        email: formData.email || '',
        phone: formData.phone || '',
        address: formData.address || {
          street: '',
          city: '',
          state: '',
          zipCode: ''
        },
        policyTypes: formData.policyTypes || [],
        isActive: formData.isActive ?? true,
        rating: formData.rating || 0,
        totalClaims: formData.totalClaims || 0,
        avgProcessingTime: formData.avgProcessingTime || 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      setCarriers([...carriers, newCarrier]);
    }
    setIsModalOpen(false);
  };

  return (
    <div className="space-y-6">
      <PageHeader
        title="Carriers"
        description="Manage insurance carriers and their information"
      >
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Carrier
        </Button>
      </PageHeader>

      <PageContent>
        <DataTable
          data={carriers}
          columns={columns}
          searchPlaceholder="Search carriers..."
          onRowClick={(carrier) => console.log('View carrier:', carrier)}
        />
      </PageContent>

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title={editingCarrier ? 'Edit Carrier' : 'Add New Carrier'}
        size="lg"
        footer={
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setIsModalOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSave}>
              {editingCarrier ? 'Update' : 'Create'}
            </Button>
          </div>
        }
      >
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="name">Company Name</Label>
            <Input
              id="name"
              value={formData.name || ''}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder="Enter company name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="code">Code</Label>
            <Input
              id="code"
              value={formData.code || ''}
              onChange={(e) => setFormData({ ...formData, code: e.target.value })}
              placeholder="Enter carrier code"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="contactPerson">Contact Person</Label>
            <Input
              id="contactPerson"
              value={formData.contactPerson || ''}
              onChange={(e) => setFormData({ ...formData, contactPerson: e.target.value })}
              placeholder="Enter contact person name"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input
              id="email"
              type="email"
              value={formData.email || ''}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              placeholder="Enter email address"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone || ''}
              onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
              placeholder="Enter phone number"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="rating">Rating</Label>
            <Input
              id="rating"
              type="number"
              min="0"
              max="5"
              step="0.1"
              value={formData.rating || ''}
              onChange={(e) => setFormData({ ...formData, rating: parseFloat(e.target.value) })}
              placeholder="Enter rating (0-5)"
            />
          </div>
          <div className="flex items-center space-x-2 col-span-2">
            <Switch
              id="isActive"
              checked={formData.isActive ?? true}
              onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
            />
            <Label htmlFor="isActive">Active</Label>
          </div>
        </div>
      </Modal>
    </div>
  );
}
