// Core entity types for the insurance consulting application

export interface Carrier {
  id: string;
  name: string;
  code: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  policyTypes: string[];
  isActive: boolean;
  rating: number; // 1-5 stars
  totalClaims: number;
  avgProcessingTime: number; // in days
  createdAt: Date;
  updatedAt: Date;
}

export interface Adjuster {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  specializations: string[];
  assignedCarriers: string[]; // Carrier IDs
  licenseNumber: string;
  licenseExpiry: Date;
  isActive: boolean;
  rating: number; // 1-5 stars
  totalAssignments: number;
  avgCompletionTime: number; // in days
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  emergencyContact: {
    name: string;
    phone: string;
    relationship: string;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface Claim {
  id: string;
  claimNumber: string;
  carrierId: string;
  adjusterId?: string;
  contractorId?: string;
  policyNumber: string;
  policyHolderName: string;
  policyHolderPhone: string;
  policyHolderEmail: string;
  incidentDate: Date;
  reportedDate: Date;
  claimType: string;
  category: string;
  subcategory?: string;
  status: 'Open' | 'In Progress' | 'Under Review' | 'Approved' | 'Denied' | 'Closed';
  priority: 'Low' | 'Medium' | 'High' | 'Critical';
  description: string;
  damageDescription: string;
  estimatedAmount: number;
  approvedAmount?: number;
  deductible: number;
  location: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    coordinates?: {
      lat: number;
      lng: number;
    };
  };
  assignedDate?: Date;
  completedDate?: Date;
  notes: ClaimNote[];
  documents: ClaimDocument[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ClaimNote {
  id: string;
  claimId: string;
  authorId: string;
  authorName: string;
  content: string;
  isInternal: boolean;
  createdAt: Date;
}

export interface ClaimDocument {
  id: string;
  claimId: string;
  name: string;
  type: string;
  url: string;
  uploadedBy: string;
  uploadedAt: Date;
}

export interface Contractor {
  id: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  licenseNumber: string;
  licenseExpiry: Date;
  servicesOffered: string[];
  specializations: string[];
  rating: number; // 1-5 stars
  totalJobs: number;
  avgCompletionTime: number; // in days
  isActive: boolean;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
  serviceArea: string[]; // List of cities/regions they serve
  insurance: {
    provider: string;
    policyNumber: string;
    expiryDate: Date;
    coverageAmount: number;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface Category {
  id: string;
  name: string;
  type: 'claim' | 'service' | 'damage';
  parentId?: string;
  description: string;
  isActive: boolean;
  sortOrder: number;
  children?: Category[];
  createdAt: Date;
  updatedAt: Date;
}

export interface Parameter {
  id: string;
  key: string;
  name: string;
  value: string | number | boolean;
  type: 'string' | 'number' | 'boolean' | 'json';
  category: string;
  description: string;
  isEditable: boolean;
  validationRules?: {
    min?: number;
    max?: number;
    pattern?: string;
    required?: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

// Dashboard and analytics types
export interface DashboardMetrics {
  openClaims: number;
  closedClaims: number;
  activeConsultations: number;
  totalRevenue: number;
  avgProcessingTime: number;
  carrierPerformance: CarrierPerformance[];
  recentActivity: ActivityItem[];
  claimsByStatus: ClaimStatusCount[];
  claimsByMonth: MonthlyClaimData[];
  topAdjusters: AdjusterPerformance[];
}

export interface CarrierPerformance {
  carrierId: string;
  carrierName: string;
  totalClaims: number;
  avgProcessingTime: number;
  satisfactionRating: number;
  revenue: number;
}

export interface ActivityItem {
  id: string;
  type: 'claim_created' | 'claim_assigned' | 'claim_completed' | 'adjuster_added' | 'carrier_updated';
  title: string;
  description: string;
  timestamp: Date;
  userId?: string;
  userName?: string;
}

export interface ClaimStatusCount {
  status: string;
  count: number;
  percentage: number;
}

export interface MonthlyClaimData {
  month: string;
  openClaims: number;
  closedClaims: number;
  revenue: number;
}

export interface AdjusterPerformance {
  adjusterId: string;
  adjusterName: string;
  totalClaims: number;
  avgCompletionTime: number;
  rating: number;
  revenue: number;
}

// Filter and search types
export interface DateRange {
  start: Date;
  end: Date;
}

export interface ClaimFilters {
  status?: string[];
  carrierId?: string[];
  adjusterId?: string[];
  priority?: string[];
  claimType?: string[];
  dateRange?: DateRange;
  amountRange?: {
    min: number;
    max: number;
  };
}

export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface SearchParams {
  query: string;
  filters?: any;
  pagination?: PaginationParams;
}
