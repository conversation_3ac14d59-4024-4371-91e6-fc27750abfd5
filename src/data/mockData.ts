import { v4 as uuidv4 } from 'uuid';
import { 
  Carrier, 
  Adjuster, 
  Claim, 
  Contractor, 
  Category, 
  Parameter,
  DashboardMetrics,
  ActivityItem,
  ClaimStatusCount,
  MonthlyClaimData,
  CarrierPerformance,
  AdjusterPerformance
} from '@/types';

// Mock Carriers
export const mockCarriers: Carrier[] = [
  {
    id: uuidv4(),
    name: "State Farm Insurance",
    code: "SF",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 State Farm Plaza",
      city: "Bloomington",
      state: "IL",
      zipCode: "61710"
    },
    policyTypes: ["Auto", "Home", "Life", "Commercial"],
    isActive: true,
    rating: 4.5,
    totalClaims: 1247,
    avgProcessingTime: 12,
    createdAt: new Date('2020-01-15'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: uuidv4(),
    name: "Allstate Insurance",
    code: "AS",
    contactPerson: "<PERSON>",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "2775 Sanders Rd",
      city: "Northbrook",
      state: "IL",
      zipCode: "60062"
    },
    policyTypes: ["Auto", "Home", "Renters", "Commercial"],
    isActive: true,
    rating: 4.2,
    totalClaims: 892,
    avgProcessingTime: 15,
    createdAt: new Date('2020-03-20'),
    updatedAt: new Date('2024-06-10')
  },
  {
    id: uuidv4(),
    name: "Progressive Insurance",
    code: "PG",
    contactPerson: "Michael Chen",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "6300 Wilson Mills Rd",
      city: "Mayfield Village",
      state: "OH",
      zipCode: "44143"
    },
    policyTypes: ["Auto", "Motorcycle", "Commercial Auto"],
    isActive: true,
    rating: 4.0,
    totalClaims: 1156,
    avgProcessingTime: 10,
    createdAt: new Date('2020-05-10'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    name: "GEICO Insurance",
    code: "GC",
    contactPerson: "Lisa Rodriguez",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 GEICO Plaza",
      city: "Chevy Chase",
      state: "MD",
      zipCode: "20815"
    },
    policyTypes: ["Auto", "Motorcycle", "Home", "Renters"],
    isActive: true,
    rating: 4.3,
    totalClaims: 2134,
    avgProcessingTime: 8,
    createdAt: new Date('2019-11-05'),
    updatedAt: new Date('2024-06-25')
  },
  {
    id: uuidv4(),
    name: "Liberty Mutual",
    code: "LM",
    contactPerson: "David Thompson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "175 Berkeley St",
      city: "Boston",
      state: "MA",
      zipCode: "02116"
    },
    policyTypes: ["Auto", "Home", "Life", "Commercial", "Workers Comp"],
    isActive: true,
    rating: 4.1,
    totalClaims: 756,
    avgProcessingTime: 14,
    createdAt: new Date('2021-02-12'),
    updatedAt: new Date('2024-06-18')
  },
  {
    id: uuidv4(),
    name: "Farmers Insurance",
    code: "FI",
    contactPerson: "Amanda Wilson",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "4680 Wilshire Blvd",
      city: "Los Angeles",
      state: "CA",
      zipCode: "90010"
    },
    policyTypes: ["Auto", "Home", "Life", "Business"],
    isActive: true,
    rating: 3.9,
    totalClaims: 634,
    avgProcessingTime: 16,
    createdAt: new Date('2021-08-30'),
    updatedAt: new Date('2024-06-12')
  },
  {
    id: uuidv4(),
    name: "Nationwide Insurance",
    code: "NW",
    contactPerson: "Robert Garcia",
    email: "<EMAIL>",
    phone: "(*************",
    address: {
      street: "1 Nationwide Plaza",
      city: "Columbus",
      state: "OH",
      zipCode: "43215"
    },
    policyTypes: ["Auto", "Home", "Life", "Pet", "Commercial"],
    isActive: false,
    rating: 4.4,
    totalClaims: 423,
    avgProcessingTime: 11,
    createdAt: new Date('2022-01-15'),
    updatedAt: new Date('2024-05-20')
  }
];

// Mock Adjusters
export const mockAdjusters: Adjuster[] = [
  {
    id: uuidv4(),
    firstName: "Emily",
    lastName: "Carter",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Auto Claims", "Property Damage", "Liability"],
    assignedCarriers: [mockCarriers[0].id, mockCarriers[1].id],
    licenseNumber: "ADJ-12345",
    licenseExpiry: new Date('2025-12-31'),
    isActive: true,
    rating: 4.8,
    totalAssignments: 156,
    avgCompletionTime: 8,
    address: {
      street: "123 Main St",
      city: "Chicago",
      state: "IL",
      zipCode: "60601"
    },
    emergencyContact: {
      name: "James Carter",
      phone: "(*************",
      relationship: "Spouse"
    },
    createdAt: new Date('2021-03-15'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    firstName: "Marcus",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Commercial Claims", "Workers Compensation", "Fraud Investigation"],
    assignedCarriers: [mockCarriers[2].id, mockCarriers[3].id],
    licenseNumber: "ADJ-23456",
    licenseExpiry: new Date('2026-06-30'),
    isActive: true,
    rating: 4.6,
    totalAssignments: 203,
    avgCompletionTime: 12,
    address: {
      street: "456 Oak Ave",
      city: "Cleveland",
      state: "OH",
      zipCode: "44101"
    },
    emergencyContact: {
      name: "Maria Johnson",
      phone: "(*************",
      relationship: "Sister"
    },
    createdAt: new Date('2020-08-20'),
    updatedAt: new Date('2024-06-18')
  },
  {
    id: uuidv4(),
    firstName: "Sarah",
    lastName: "Williams",
    email: "<EMAIL>",
    phone: "(*************",
    specializations: ["Property Claims", "Water Damage", "Fire Damage"],
    assignedCarriers: [mockCarriers[0].id, mockCarriers[4].id],
    licenseNumber: "ADJ-34567",
    licenseExpiry: new Date('2025-09-15'),
    isActive: true,
    rating: 4.9,
    totalAssignments: 189,
    avgCompletionTime: 7,
    address: {
      street: "789 Pine St",
      city: "Boston",
      state: "MA",
      zipCode: "02101"
    },
    emergencyContact: {
      name: "David Williams",
      phone: "(*************",
      relationship: "Father"
    },
    createdAt: new Date('2021-01-10'),
    updatedAt: new Date('2024-06-22')
  }
];

// Mock Categories
export const mockCategories: Category[] = [
  {
    id: uuidv4(),
    name: "Auto Claims",
    type: "claim",
    description: "All automotive related claims",
    isActive: true,
    sortOrder: 1,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    name: "Collision",
    type: "claim",
    parentId: "auto-claims-id",
    description: "Vehicle collision claims",
    isActive: true,
    sortOrder: 1,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    name: "Property Claims",
    type: "claim",
    description: "Property damage claims",
    isActive: true,
    sortOrder: 2,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    name: "Water Damage",
    type: "damage",
    description: "Water related damage",
    isActive: true,
    sortOrder: 1,
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  }
];

// Mock Parameters
export const mockParameters: Parameter[] = [
  {
    id: uuidv4(),
    key: "max_claim_amount",
    name: "Maximum Claim Amount",
    value: 1000000,
    type: "number",
    category: "Claims",
    description: "Maximum amount for a single claim",
    isEditable: true,
    validationRules: {
      min: 1000,
      max: 10000000,
      required: true
    },
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: uuidv4(),
    key: "auto_assign_threshold",
    name: "Auto Assignment Threshold",
    value: 72,
    type: "number",
    category: "Workflow",
    description: "Hours before auto-assigning unassigned claims",
    isEditable: true,
    validationRules: {
      min: 1,
      max: 168,
      required: true
    },
    createdAt: new Date('2020-01-01'),
    updatedAt: new Date('2024-01-01')
  }
];

// Mock Contractors
export const mockContractors: Contractor[] = [
  {
    id: uuidv4(),
    companyName: "Elite Auto Repair",
    contactPerson: "Tony Russo",
    email: "<EMAIL>",
    phone: "(*************",
    licenseNumber: "CONT-11111",
    licenseExpiry: new Date('2025-12-31'),
    servicesOffered: ["Auto Body Repair", "Mechanical Repair", "Towing"],
    specializations: ["Collision Repair", "Paint Work", "Frame Straightening"],
    rating: 4.7,
    totalJobs: 342,
    avgCompletionTime: 5,
    isActive: true,
    address: {
      street: "1234 Industrial Blvd",
      city: "Chicago",
      state: "IL",
      zipCode: "60622"
    },
    serviceArea: ["Chicago", "Evanston", "Oak Park", "Cicero"],
    insurance: {
      provider: "Commercial Insurance Co",
      policyNumber: "CIC-789456",
      expiryDate: new Date('2025-06-30'),
      coverageAmount: 2000000
    },
    createdAt: new Date('2021-05-15'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    companyName: "Precision Property Restoration",
    contactPerson: "Jennifer Lee",
    email: "<EMAIL>",
    phone: "(*************",
    licenseNumber: "CONT-22222",
    licenseExpiry: new Date('2026-03-15'),
    servicesOffered: ["Water Damage Restoration", "Fire Damage Repair", "Mold Remediation"],
    specializations: ["Emergency Response", "Structural Drying", "Content Restoration"],
    rating: 4.9,
    totalJobs: 156,
    avgCompletionTime: 8,
    isActive: true,
    address: {
      street: "567 Restoration Way",
      city: "Boston",
      state: "MA",
      zipCode: "02118"
    },
    serviceArea: ["Boston", "Cambridge", "Somerville", "Newton"],
    insurance: {
      provider: "Restoration Insurance Group",
      policyNumber: "RIG-456789",
      expiryDate: new Date('2025-09-30'),
      coverageAmount: 5000000
    },
    createdAt: new Date('2020-11-20'),
    updatedAt: new Date('2024-06-15')
  },
  {
    id: uuidv4(),
    companyName: "Metro Glass & Glazing",
    contactPerson: "Carlos Martinez",
    email: "<EMAIL>",
    phone: "(*************",
    licenseNumber: "CONT-33333",
    licenseExpiry: new Date('2025-08-20'),
    servicesOffered: ["Glass Replacement", "Window Repair", "Windshield Replacement"],
    specializations: ["Commercial Glass", "Residential Windows", "Auto Glass"],
    rating: 4.4,
    totalJobs: 289,
    avgCompletionTime: 2,
    isActive: true,
    address: {
      street: "890 Glass Street",
      city: "Cleveland",
      state: "OH",
      zipCode: "44115"
    },
    serviceArea: ["Cleveland", "Akron", "Canton", "Youngstown"],
    insurance: {
      provider: "Glazier's Insurance",
      policyNumber: "GI-123456",
      expiryDate: new Date('2025-12-15'),
      coverageAmount: 1500000
    },
    createdAt: new Date('2021-09-10'),
    updatedAt: new Date('2024-06-18')
  }
];

// Mock Claims
export const mockClaims: Claim[] = [
  {
    id: uuidv4(),
    claimNumber: "CLM-2024-001234",
    carrierId: mockCarriers[0].id,
    adjusterId: mockAdjusters[0].id,
    contractorId: mockContractors[0].id,
    policyNumber: "SF-POL-789456",
    policyHolderName: "Robert Anderson",
    policyHolderPhone: "(*************",
    policyHolderEmail: "<EMAIL>",
    incidentDate: new Date('2024-06-15'),
    reportedDate: new Date('2024-06-16'),
    claimType: "Auto",
    category: "Collision",
    status: "In Progress",
    priority: "Medium",
    description: "Rear-end collision at intersection",
    damageDescription: "Significant rear bumper damage, trunk damage, possible frame damage",
    estimatedAmount: 8500,
    approvedAmount: 7800,
    deductible: 500,
    location: {
      street: "Main St & Oak Ave",
      city: "Chicago",
      state: "IL",
      zipCode: "60601",
      coordinates: {
        lat: 41.8781,
        lng: -87.6298
      }
    },
    assignedDate: new Date('2024-06-17'),
    notes: [
      {
        id: uuidv4(),
        claimId: "claim-id",
        authorId: mockAdjusters[0].id,
        authorName: "Emily Carter",
        content: "Initial inspection completed. Damage assessment in progress.",
        isInternal: false,
        createdAt: new Date('2024-06-18')
      }
    ],
    documents: [
      {
        id: uuidv4(),
        claimId: "claim-id",
        name: "Police Report",
        type: "PDF",
        url: "/documents/police-report-001234.pdf",
        uploadedBy: "Robert Anderson",
        uploadedAt: new Date('2024-06-16')
      }
    ],
    createdAt: new Date('2024-06-16'),
    updatedAt: new Date('2024-06-20')
  },
  {
    id: uuidv4(),
    claimNumber: "CLM-2024-001235",
    carrierId: mockCarriers[1].id,
    adjusterId: mockAdjusters[1].id,
    policyNumber: "AS-POL-456789",
    policyHolderName: "Maria Gonzalez",
    policyHolderPhone: "(*************",
    policyHolderEmail: "<EMAIL>",
    incidentDate: new Date('2024-06-10'),
    reportedDate: new Date('2024-06-11'),
    claimType: "Property",
    category: "Water Damage",
    status: "Open",
    priority: "High",
    description: "Burst pipe caused flooding in basement",
    damageDescription: "Basement flooding, damaged flooring, walls, and personal property",
    estimatedAmount: 15000,
    deductible: 1000,
    location: {
      street: "456 Elm Street",
      city: "Northbrook",
      state: "IL",
      zipCode: "60062"
    },
    assignedDate: new Date('2024-06-12'),
    notes: [],
    documents: [],
    createdAt: new Date('2024-06-11'),
    updatedAt: new Date('2024-06-19')
  }
];

// Dashboard Metrics
export const mockDashboardMetrics: DashboardMetrics = {
  openClaims: 156,
  closedClaims: 1247,
  activeConsultations: 89,
  totalRevenue: 2456789,
  avgProcessingTime: 11.5,
  carrierPerformance: [
    {
      carrierId: mockCarriers[0].id,
      carrierName: mockCarriers[0].name,
      totalClaims: 1247,
      avgProcessingTime: 12,
      satisfactionRating: 4.5,
      revenue: 856432
    },
    {
      carrierId: mockCarriers[1].id,
      carrierName: mockCarriers[1].name,
      totalClaims: 892,
      avgProcessingTime: 15,
      satisfactionRating: 4.2,
      revenue: 634521
    }
  ],
  recentActivity: [
    {
      id: uuidv4(),
      type: "claim_created",
      title: "New Claim Created",
      description: "CLM-2024-001234 - Auto collision claim",
      timestamp: new Date('2024-06-20T10:30:00'),
      userId: "user-1",
      userName: "Emily Carter"
    },
    {
      id: uuidv4(),
      type: "claim_assigned",
      title: "Claim Assigned",
      description: "CLM-2024-001235 assigned to Marcus Johnson",
      timestamp: new Date('2024-06-20T09:15:00'),
      userId: "user-2",
      userName: "System"
    }
  ],
  claimsByStatus: [
    { status: "Open", count: 156, percentage: 35 },
    { status: "In Progress", count: 89, percentage: 20 },
    { status: "Under Review", count: 67, percentage: 15 },
    { status: "Closed", count: 134, percentage: 30 }
  ],
  claimsByMonth: [
    { month: "Jan", openClaims: 45, closedClaims: 38, revenue: 185000 },
    { month: "Feb", openClaims: 52, closedClaims: 41, revenue: 198000 },
    { month: "Mar", openClaims: 48, closedClaims: 45, revenue: 210000 },
    { month: "Apr", openClaims: 61, closedClaims: 52, revenue: 225000 },
    { month: "May", openClaims: 58, closedClaims: 49, revenue: 215000 },
    { month: "Jun", openClaims: 67, closedClaims: 55, revenue: 245000 }
  ],
  topAdjusters: [
    {
      adjusterId: mockAdjusters[0].id,
      adjusterName: `${mockAdjusters[0].firstName} ${mockAdjusters[0].lastName}`,
      totalClaims: 156,
      avgCompletionTime: 8,
      rating: 4.8,
      revenue: 425000
    },
    {
      adjusterId: mockAdjusters[1].id,
      adjusterName: `${mockAdjusters[1].firstName} ${mockAdjusters[1].lastName}`,
      totalClaims: 203,
      avgCompletionTime: 12,
      rating: 4.6,
      revenue: 567000
    }
  ]
};
