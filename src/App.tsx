import './App.css'
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/context/ThemeContext";
import { TooltipProvider } from "@/components/ui/tooltip";
import { Layout } from '@/components/layout/Layout';
import { NotFound } from '@/pages/NotFound';
import { AssignmentPage } from '@/pages/Assignment';
import { Dashboard } from '@/pages/Dashboard';
import { Carriers } from '@/pages/Carriers';
import { Adjusters } from '@/pages/Adjusters';
import { Contractors } from '@/pages/Contractors';
import { Categories } from '@/pages/Categories';
import { Parameters } from '@/pages/Parameters';

function App() {
  return (
    <ThemeProvider>
      <TooltipProvider>
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route path="/" element={<Dashboard />} />
              <Route path="assignment" element={<AssignmentPage />} />
              <Route path="carriers" element={<Carriers />} />
              <Route path="adjusters" element={<Adjusters />} />
              <Route path="contractors" element={<Contractors />} />
              <Route path="categories" element={<Categories />} />
              <Route path="parameters" element={<Parameters />} />
              <Route path="*" element={<NotFound />} />
            </Route>
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </ThemeProvider>
  )
}

export default App
