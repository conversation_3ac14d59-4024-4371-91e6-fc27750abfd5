
import { NavLink } from 'react-router-dom';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Home,
  ClipboardList,
  Building2,
  Users,
  HardHat,
  FolderTree,
  Settings
} from "lucide-react";

interface SidebarProps {
  className?: string;
  isOpen: boolean;
}

interface SidebarItemProps {
  icon: React.ElementType;
  label: string;
  path: string;
}

function SidebarItem({ icon: Icon, label, path }: SidebarItemProps) {
  return (
    <NavLink
      to={path}
      className={({ isActive }) => cn(
        isActive ? "text-primary" : "text-sidebar-foreground"
      )}
    >
      {({ isActive }) => (
        <Button
          variant={isActive ? "secondary" : "ghost"}
          className={cn(
            "w-full justify-start gap-2",
            isActive && "bg-primary/10 dark:bg-primary/30 font-medium"
          )}
        >
          <Icon className="h-5 w-5" />
          <span>{label}</span>
        </Button>
      )}
    </NavLink>
  );
}

export function Sidebar({ className, isOpen }: SidebarProps) {  

  return (
    <aside
      className={cn(
        "fixed inset-y-0 left-0 z-30 flex w-60 flex-col border-r bg-sidebar transition-transform duration-300 ease-in-out",
        isOpen ? "translate-x-0" : "-translate-x-full",
        className
      )}
    >
      <div className="flex h-16 items-center border-b px-6">
        <div className="flex items-center gap-2">
        <img src="/logo.png" alt="" className="h-8 w-8" />
          <span className="font-bold text-xl">Phillips</span>
        </div>
      </div>
      <ScrollArea className="flex-1 px-4 py-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-xs font-medium text-sidebar-foreground/70 px-2">
              MAIN MENU
            </h3>
            <div className="space-y-1">
              <SidebarItem icon={Home} label="Dashboard" path="/"/>
              <SidebarItem icon={ClipboardList} label="Claims" path="/assignment"/>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-xs font-medium text-sidebar-foreground/70 px-2">
              MANAGEMENT
            </h3>
            <div className="space-y-1">
              <SidebarItem icon={Building2} label="Carriers" path="/carriers"/>
              <SidebarItem icon={Users} label="Adjusters" path="/adjusters"/>
              <SidebarItem icon={HardHat} label="Contractors" path="/contractors"/>
              <SidebarItem icon={FolderTree} label="Categories" path="/categories"/>
              <SidebarItem icon={Settings} label="Parameters" path="/parameters"/>
            </div>
          </div>

        </div>
      </ScrollArea>
     {/*  <div className="border-t p-4">
        <Button variant="outline" className="w-full">
          <svg className="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10" />
            <line x1="12" y1="8" x2="12" y2="16" />
            <line x1="8" y1="12" x2="16" y2="12" />
          </svg>
          Add New Service
        </Button>
      </div> */}
    </aside>
  );
}
